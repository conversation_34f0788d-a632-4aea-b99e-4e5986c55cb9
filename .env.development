ENV = "development"
# 是否打开mock
VITE_USE_MOCK = false

# 发布路径
VITE_PUBLIC_PATH = ./

# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/api","http://localhost:3100"],["/upload","http://localhost:3100/upload"]]

# 控制台不输出
VITE_DROP_CONSOLE = false

# 是否开启调试工具 Vconsole
VITE_OPEN_VCONSOLE = false

# H5接口父地址(必填)
VITE_GLOB_API_URL=

# H5接口全路径地址(必填)
 VITE_GLOB_BASE_URL=http://**************:9012
#  VITE_GLOB_BASE_URL=http://company.maisuiedu.com/api
# VITE_GLOB_BASE_URL=https://xxxxx.com/v1  # 测试服接口

# 接口前缀
VITE_GLOB_API_URL_PREFIX=
# 教材封面
VITE_GLOB_FILE_URL=http://**************:9000/
# 教材阅读
VITE_GLOB_READ_URL=http://************:81

# DeepSeek API Key (请替换为您的实际API Key)
VITE_DEEPSEEK_API_KEY=***********************************
