<template>
  <el-dialog
    v-model="dialogVisible"
    width="493px"
    top="30vh"
    class="msgdialog"
    :title="tname"
    @close="close"
    :style="{
        '--el-dialog-padding-primary':'0px',
    }"
  >
    <img class="msgicon" :src="imgUrl[messageType]" alt="" />
    <div class="msgcss">{{ message }}</div>
    <div class="btns">
        <el-button class="linebtn" @click="close">取消</el-button>
        <el-button class="defbtn40" @click="onDelete">确认删除</el-button>
      </div>
  </el-dialog>
</template>

<script setup>
import { reactive } from "vue"
import errorimg from '@/assets/errorimg.png';
import successimg from '@/assets/successimg.png';

const dialogVisible = ref(false)
const messageType = ref('success')
const tname = ref('')
const message = ref('')
const imgUrl = reactive({
    success:successimg,
    error:errorimg,
    delete:errorimg
})
const closeAction = ref(null)
const deleteData = ref(null)
function show({ type, title, msg, onClose,deleteAction }) {
    messageType.value = type
    console.log('sssssss',deleteAction)
    tname.value = title
    message.value = msg
    closeAction.value = onClose
    deleteData.value = deleteAction
    dialogVisible.value = true
}
function close() {
    dialogVisible.value = false
    if (closeAction.value) {
        closeAction.value()
    }
}
function onDelete(){
  dialogVisible.value = false
  if (deleteData.value){
    deleteData.value()
  }
}
defineExpose({
  show,
})
</script>
<style lang="scss">
.msgdialog {
  .el-dialog {
    border-radius: 8px !important;
  }
  .el-dialog__header{
      padding: 24px 30px;
      background: linear-gradient( 180deg, #E7EDFE 0%, rgba(231,237,254,0) 100%) !important;
  }
  .el-dialog__body{
    text-align: center !important;
    padding-bottom: 30px !important;
    min-height: 250px !important;
  }
    .el-dialog__headerbtn{
        width: 24px !important;
        height: 24px !important;
        font-size: 20px !important;
        right: 30px;
        top: 24px;
        .el-icon{
            width: 24px;
            height: 24px;
            color: #2D2F33 !important;
        }
    }
    
}
</style>
<style lang="scss" scoped>
.btns{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  .el-button +.el-button {
    margin-right: 16px;
  }
}
.msgicon {
  width: 62px;
  height: 62px;
  margin: 0 auto !important;
  margin-top: 20px !important;
}
.titlename {
  margin-top: 16px;
  height: 30px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #333333;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.msgcss {
  margin-top: 24px;
  height: 27px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 18px;
  color: #44474D;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
</style>
