<template>
  <div class="bg">
    <Banner></Banner>
    <div class="main">
      <div class="mainContent">
        <div class="mainTitle">学习推荐</div>
        <div class="gridContainer">
          <div
            class="contentItem"
            v-for="(item, index) in videoList"
            :key="index"
            @click="playVideo(item.id)"
          >
            <div class="videoCard">
              <img :src="item.coverUrl" alt="" class="topicImg" />
              <div
                class="videoTag"
                v-if="item.tag"
                :style="{ background: getTagColor(item.tag) }"
              >
                {{ item.tag }}
              </div>
            </div>
            <div class="videoMsg">
              <el-text class="contentTitle" truncated>{{ item.name }}</el-text>
              <el-text truncated class="contentName">主讲人：{{ item.lecturerName }}</el-text>
            </div>
          </div>
        </div>
      </div>
      <div class="zixun">
        <div class="main">
          <div class="news">
            <div class="mainTitle">动态资讯</div>
            <div class="newsList">
              <div
                class="newsItem"
                v-for="(item, index) in newsList"
                :key="index"
                @click="goNewDetail(item)"
              >
                <div class="newsTop">
                  <div class="newNumber">
                    {{ index + 1 < 10 ? '0' + (index + 1) : index + 1 }}
                  </div>
                  <div class="sort">
                    <img src="@/assets/dian.png" alt="" />
                    <div class="newsTitle">{{ item.title }}</div>
                  </div>
                </div>
                <div class="newsContent">{{ item.content }}</div>
                <div class="newsBottom">
                  <div class="left">
                    <img src="@/assets/time.png" alt="" class="timeImg" />
                    <div class="time">{{ item.time }}</div>
                  </div>
                  <div class="right">
                    <img src="@/assets/eye.png" alt="" class="eyeImg" />
                    <div class="eye">{{ item.read }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mainContent pb32">
        <div class="mainTitle">AI工具精选</div>
        <div class="gridContainer">
          <div
            class="aiToolItem"
            v-for="(tool, index) in aiTools"
            :key="index"
            @click="handleToolClick(tool.url)"
          >
            <div class="mt20">
              <div class="aiToolItemTop">
                <img :src="tool.icon" alt="" class="aiToolImg" />
                <div class="aiToolText">{{ tool.name }}</div>
              </div>
              <div class="aiToolItemBottom">
                <div class="bottomtext">{{ tool.description }}</div>
                <div class="biaoqian">
                  <div
                    class="biaoqianItem"
                    v-for="(tag, tagIndex) in tool.tags"
                    :key="tagIndex"
                  >
                    <div class="biaoqiantext">{{ tag }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Banner from '@/components/home/<USER>'
import { useRouter } from 'vue-router'
import { bannerList, courseList } from '@/api/api'
import icon1 from '@/assets/deepseek.png'
import icon2 from '@/assets/mita.png'
import icon3 from '@/assets/wenxin.png'
import icon4 from '@/assets/kimi.png'
import icon5 from '@/assets/doubao.png'
import icon6 from '@/assets/tongyi.png'
import icon7 from '@/assets/keling.png'
import icon8 from '@/assets/jimeng.png'

const router = useRouter()
const videoList = ref([])
const newsList = ref([
  {
    title: '《国家安全教育大学生读本》出版发行',
    content:
      '为推动总体国家安全观进教材进课堂进头脑，由教育部和中央有关部门组织编写的马克思主义理论研究和建设工程重点教材《国家安全教育大学生读本》（以下简称《读本》），已由高等教育出版社出版。',
    time: '2024-12-26',
    read: '996',
  },
  {
    title: '重磅！教育部发布2023年全国教育事业发展统计公报',
    content:
      '依据《中华人民共和国学位条例暂行实施办法》《学士学位授权与授予管理办法》《关于做好本科层次职业学校学士学位授权与授予工作的意见》等有关规定，教育部职业教育与成人教育司会同学位管理与研究生教育司（国务院学位委员会办公室）组织力量制订《高职本科专业学士学位授予学科门类对应表》，并于12月12日通过教育部官方网站正式发布。',
    time: '2025-06-20',
    read: '852',
  },
  {
    title: '教育部发布高职本科专业学士学位授予学科门类对应表',
    content:
      '为深入贯彻全国教育大会精神，进一步加强教材建设和管理，根据《教育部办公厅关于组织开展第二批“十四五”职业教育国家规划教材遴选工作的通知》（教职成厅函〔2025〕1号）要求，经研究，决定开展第二批“十四五”职业教育河南省规划教材暨第二批“十四五”职业教育国家规划教材遴选工作，现将有关事项通知如下',
    time: '2025-06-20',
    read: '852',
  },
  {
    title:
      '河南省教育厅办公室关于开展第二批“十四五”职业教育河南省规划教材暨第二批“十四五”职业教育国家规划教材遴选推荐工作的通知',
    content:
      'AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选',
    time: '2025-06-20',
    read: '852',
  },
  {
    title: '如何建好数字教材？把握这“三个特征”是关键',
    content:
      '随着新一代信息技术、数字媒体技术的广泛应用，数字化成为当前职业教育改革的重要技术支撑，数字教材等新形态教学工具应运而生。相比于传统教材，数字教材有更好的呈现形式、交互能力及查阅便利，能够解决纸质教材课堂使用效率低、课后使用频次低等问题。目前，职教领域对数字教材的研究与探索正进入快速发展阶段，各类形态的数字教材层出不穷，但仍没有统一的定义和标准。部分数字教材仍是在纸质教材版式的基础上完成数字化改造，尝试将阅读观感与数字资源呈现特点相匹配，形式上更像是电子教材的升级版本，没有打破传统教材的限界藩篱，只在呈现手段、阅读便利等方面有了改善。',
    time: '2025-06-20',
    read: '852',
  },
  {
    title: '教材数字化：从纸上到云端，看中国教育的蜕变！',
    content:
      'AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选AI工具精选',
    time: '2025-06-20',
    read: '852',
  },
])
const aiTools = ref([
  {
    icon: icon1,
    name: 'DeepSeek',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['推荐', '热门', 'CN'],
    url: 'https://www.deepseek.com',
  },
  {
    icon: icon2,
    name: '秘塔',
    description:
      '告别广告干扰，开启高效搜索！秘塔 AI 搜索引擎，直击结果，用实力定义实用',
    tags: ['热门', 'CN'],
    url: 'https://metaso.cn',
  },
  {
    icon: icon3,
    name: '文心一言',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['CN'],
    url: 'https://yiyan.baidu.com',
  },
  {
    icon: icon4,
    name: 'Kimi',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['推荐', 'CN'],
    url: 'https://kimi.moonshot.cn',
  },
  {
    icon: icon5,
    name: '豆包',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['CN'],
    url: 'https://www.doubao.com',
  },
  {
    icon: icon6,
    name: '通义千问',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['推荐', 'CN'],
    url: 'https://tongyi.aliyun.com',
  },
  {
    icon: icon7,
    name: '可灵',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['推荐', 'CN'],
    url: 'https://klingai.kuaishou.com',
  },
  {
    icon: icon8,
    name: '即梦',
    description: '代码难题瞬间攻克，数学算法脱口而出，复杂问题秒变易懂！',
    tags: ['CN'],
    url: 'https://jimeng.jianying.com',
  },
])

function handleToolClick(url) {
  if (url) {
    window.open(url, '_blank')
  }
}

const carouselItem = ref([])

const fetchBannerList = () => {
  bannerList({
    issueType: 1,
    type: 1,
    isEnabled: 1,
  }).then((res) => {
    if (res.data && res.data.length > 0) {
      carouselItem.value = res.data.map((item) => ({
        imageUrl: item.imageUrl,
        title: item.title,
        text: item.text,
      }))
    }
  })
}

function playVideo(id) {
  router.push({
    path: '/coursecenter/detail',
    query: {
      id: id,
    },
  })
}

function goNewDetail(item) {
  router.push({
    path: '/newdetail',
    query: {
      id: item.id,
    },
  })
}

const getVideoList = () => {
  courseList({
    pageNum: 1,
    pageSize: 8,
    courseType: 1,
    isRecommend: 1,
  }).then((res) => {
    videoList.value = res.data
  })
}

onMounted(() => {
  // fetchBannerList()
  getVideoList()
})
</script>

<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
}
.main {
  width: 1200px;
  margin: 0 auto;
}

.mainTitle {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 20px;
  color: #2d2f33;
  margin-bottom: 16px;
}
.mainContent {
  margin-top: 34px;
}
.aiToolItem {
  width: 305px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0px 2px 20px 0px rgba(56, 108, 252, 0.16);
    cursor: pointer;

    .aiToolText {
      color: #386cf8;
      border-bottom: 2px solid #386cf8;
    }
  }
}
.topicImg {
  width: 100%;
  aspect-ratio: 16/9;
  box-shadow: 0px 2px 20px 0px rgba(56, 108, 252, 0.16);
}
.videoMsg {
  padding-left: 20px;
}
.contentTitle {
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2d2f33;
  margin-top: 12px;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.contentName {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878d99;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  width: 100%;
}
.aiToolItemTop {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}
.aiToolImg {
  width: 28px;
  height: 28px;
}
.biaoqian {
  display: flex;
  gap: 10px;
}
.biaoqianItem {
  height: 20px;
  line-height: 20px;
  background: #eff5fa;
  border-radius: 4px 4px 4px 4px;
  padding: 0 6px;
}
.biaoqiantext {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #9da4b2;
}
.bottomtext {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878d99;
  margin-bottom: 20px;
  line-height: 20px;
}
.pb32 {
  padding-bottom: 32px;
}
.mt20 {
  margin: 20px;
}
.aiToolText {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #2d2f33;
}
.videoCard {
  position: relative;
}

.videoTag {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 27px;
  line-height: 27px;
  text-align: center;
  background: #ff9b2f;
  border-radius: 8px 0px 12px 0px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #ffffff;
}

.video-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.video-player {
  width: 100%;
  height: 550px;
}

.contentItem {
  cursor: pointer;
  padding-bottom: 12px;
  border-radius: 8px;
  overflow:hidden;
  &:hover {
    box-shadow: 0px 2px 20px 0px rgba(56, 108, 252, 0.16);
  }
}
.liveItem {
  width: 305px;
  height: 366px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  position: relative;
  transition: all 0.3s ease;
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0px 2px 20px 0px rgba(56, 108, 252, 0.16);
    cursor: pointer;
    .contentTitle {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #386cfc;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
.liveImg {
  width: 100%;
  height: 154px;
  border-radius: 8px 8px 8px 8px;
}
.mt16 {
  margin: 16px;
}
.mtop16 {
  position: absolute;
  bottom: 84px;
}
.liveContent {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 13px;
  color: #878d99;
  line-height: 15px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 8px;
}
.liveContentHtml {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.liveBtn {
  width: 273px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background: #f8f9fb;
  border-radius: 4px 4px 4px 4px;
  color: #ffffff;
  position: absolute;
  bottom: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #386cfc;
  font-style: normal;
  text-transform: none;
}
.liveBtn:hover {
  color: #ffffff;
  background-color: #386cfc;
  transition: all 0.3s ease;
}

.timeline-container {
  position: relative;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 20px 0 40px;
  padding: 0;
  width: 100%;
}

.timeline-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background-color: #bacbfa;
  z-index: 1;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  transition: all 0.3s;
  width: 305px;
  margin: 0 auto;
}

.timeline-date {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 130px;
  height: 36px;
  background: #e5e9ff;
  border-radius: 99px 99px 99px 99px;
  font-size: 14px;
  color: #606266;
  .timeicon {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }
  .playicon {
    width: 24px;
    height: 24px;
  }
}

.timeline-point {
  margin-bottom: 20px;
}

.timeline-time {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}
.dataTime {
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #386cfc;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.liveCard {
  position: relative;
}
.yugao {
  width: 76px;
  height: 27px;
  background: #fc8f1a;
  border-radius: 4px 4px 4px 4px;
  position: absolute;
  top: 8px;
  left: 8px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 13px;
  color: #ffffff;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.moreBtn {
  width: 134px;
  height: 38px;
  background: #ffffff;
  border-radius: 999px 999px 999px 999px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  color: #386cfc;
  font-style: normal;
  text-transform: none;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-top: 47px;
}
.el-icon-right {
  font-size: 20px;
  color: #386cfc;
}
.zixun {
  width: 100%;
  height: 100%;
  padding-bottom: 60px;
  padding-top: 69px;
}
.newsList {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  width: 100%;
  margin-top: 31px;
}
.newsItem {
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  padding: 24px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  cursor: pointer;
  &:hover {
    transform: translateY(-8px);
    transition: all 0.3s ease;
    box-shadow: 0px 2px 20px 0px rgba(56, 108, 252, 0.16);
  }
}
.newsTop {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.newNumber {
  width: 40px;
  height: 40px;
  background: url('@/assets/sort.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: MiSans, MiSans;
  font-weight: 600;
  font-size: 24px;
  color: #ffffff;
  margin-right: 16px;
}
.newsTitle {
  height: 24px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: bold;
  font-size: 16px;
  color: #2e2f33;
  margin-top: 1px;
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sort {
  min-width: 309px;
  padding-bottom: 8px;
  border-bottom: 1px solid #386cfc;
}
.sortImg {
  width: 16px;
  height: 16px;
}
.newsContent {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #4b4d4b;
  line-height: 32px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}
.newsBottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
}
.left {
  display: flex;
  align-items: center;
}
.right {
  display: flex;
  align-items: center;
}
.timeImg {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.eyeImg {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
.time {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #8a8c89;
}
.eye {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #8a8c89;
}
</style>